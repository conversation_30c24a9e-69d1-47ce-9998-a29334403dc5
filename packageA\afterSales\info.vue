<template>
	<view class="detail_all">
		<view class="box flex justify-start flex-column align-items" style="padding-bottom: 50rpx;">

			<view class="con-center w-100 flex justify-center flex-column align-items flex-start">

				<view class="first-box flex flex-start flex-column justify-start">
					<view class="flex align-items justify-center" @click="toDetail">
						<image class="head_img" :src="info.detail.images[0]" mode=""></image>
						<view style="margin-left: 20rpx;">
							<view class="flex align-items">
								<text class="first-name" style="font-weight: 600;">{{ info.detail.title }}</text>
							</view>
							<view class="first-image flex align-items" style="margin-top: 25rpx;">
								<image :src="info.user.avatar" mode=""
									style="width: 36rpx;height: 36rpx;border-radius: 80rpx ;"></image>
								<text style="color:#9C9C9C;margin-left: 20rpx;">{{ info.user.nickname }}</text>
								<text style="color:#9C9C9C;margin:0px 20rpx;">/</text>
								<text style="color:#9C9C9C;">{{ info.join_info.people_number }}人玩过</text>
							</view>
							<view class="flex align-items" style="margin-top: 20rpx;">
								￥{{ info.totalprice }}
							</view>
						</view>
					</view>

					<text class="line"></text>
					<view class="first-image flex align-items">
						<image src="../../static/center/Alarm.png" mode="" class="icon-size"></image>
						<text>{{ formattedTime.formattedTime }}</text>
					</view>
					<text class="line"></text>

					<view class="first-image flex align-items space-between" style="width: 100%;"
						@click.stop="toMap(info.detail.latitude, info.detail.longitude, info.detail.address_detail)">
						<view class="flex" style="align-items: center;">
							<image src="/static/center/address.png" mode="" class="icon-size"></image>
							<text>{{ info.detail.address }}{{ info.detail.address_detail }}</text>
						</view>
						<image src="/static/detail/daohang.png" class="icon-size" style="width: 48rpx; height: 48rpx;">
						</image>
					</view>

				</view>


			</view>
			<view class="third flex flex-column" style="justify-content: flex-start;">
				<view class="third-top flex align-items">
					<view>退款政策
						<image class="icons" src="/static/detail/xiangqing.png"></image>
					</view>
				</view>
				<view style="padding: 30rpx;">
					<view class="refund-policy-table">
						<view class="table-container">
							<!-- 表头 -->
							<view class="table-header">
								<view class="th-item">申请退款时间</view>
								<view class="th-item">退款比例</view>
								<view class="th-item">退款金额</view>
							</view>

							<!-- 表格内容 -->
							<view class="table-body">
								<view class="table-row"  v-for="(item,index) in info.refund_desc" :key="index">
									<view class="td-item">{{item.refund_time}}</view>
									<view class="td-item">{{item.refund_scale}}</view>
									<view class="td-item">{{item.refund_price}}</view>
								</view>
							</view>
							
						</view>
					</view>
				</view>
			</view>
			<view class="third flex flex-column" style="justify-content: flex-start;">
				<view class="third-center">
					<view class="flex w-100 space-between hui align-items" style="margin-top: 8rpx;">
						<text>订单数量</text>
						<text style="color: #323232;">{{ info.num }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>订单编号</text>
						<text style="color: #323232;">{{ info.order_no }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>创建时间</text>
						<text style="color: #323232;">{{ formatTimestamp(info.createtime) }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>付款金额</text>
						<text style="color: #FF4810;font-weight: 600;">￥{{ info.payprice }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>付款方式</text>
						<text style="color: #323232;">{{ info.pay_type == 'wechat' ? '微信支付' : '余额' }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>付款时间</text>
						<text style="color: #323232;">{{ formatTimestamp(info.paytime) }}</text>
					</view>
				</view>
			</view>
			<view v-if="info.before_status!=-3" class="third flex flex-column" style="justify-content: flex-start;">
				<view class="third-center">
					<view class="flex w-100 space-between hui align-items" style="margin-top: 8rpx;">
						<text>退款数量</text>
						<text style="color: #323232;">{{ info.auth_num }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>退款金额</text>
						<text v-if="info.server_status == 3" style="color: #FF4810;font-weight: 600;">￥{{
							info.first_refundprice }}</text>
						<text v-if="info.server_status == 6" style="color: #FF4810;font-weight: 600;">￥{{
							info.total_refundprice}}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>退款原因</text>
						<text style="color: #323232;">{{ info.auth_reason }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>退回方式</text>
						<text>原路退回</text>
					</view>
				</view>
			</view>

			<view class="third flex flex-column" style="justify-content: flex-start;" >
				<view class="third-center" v-if="info.auth_status == 2">
					<view class="flex w-100 space-between hui align-items" style="margin-top: 8rpx;">
						<text>是否同意退款</text>
						<text style="color: #FF4810;">{{ '否' }}</text>
					</view>
					<view class="flex w-100 space-between hui" style="margin-top: 40rpx;">
						<text style="width: 400rpx;">驳回原因</text>
						<text style="color: #323232;">{{ info.reason }}</text>
					</view>
				</view>

				<view class="third-center" v-if="info.auth_status == 1">
					<view class="flex w-100 space-between hui align-items" style="margin-top: 8rpx;">
						<text>是否同意退款</text>
						<text style="color: #323232;">{{ "是" }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>退款数量</text>
						<text v-if="info.before_status!=-3" style="color: #323232;">{{ info.auth_num }}</text>
						<text v-if="info.before_status==-3" style="color: #323232;">{{ info.num }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>退款金额</text>
						<text v-if="info.server_status == 3" style="color: #FF4810;font-weight: 600;">￥{{
							info.first_refundprice }}</text>
						<text v-if="info.server_status == 6" style="color: #FF4810;font-weight: 600;">￥{{
							info.total_refundprice}}</text>
					</view>
				</view>
			</view>

			<!-- v-if="detailAny.feel == 0" -->
			<view v-if="info.server_status == 3" class="footer"
				style="display: flex;align-items: center;;justify-content: space-between;gap: 30rpx;">
				<view class="footer-right1 flex justify-center align-items" @click="noShow = true">
					<text> 驳回退款 </text>
				</view>
				<view class="footer-right flex justify-center align-items" @click="okShow = true">
					<text> 同意退款 </text>
				</view>
			</view>

			<u-popup @touchmove.native.stop.prevent :closeable="true" :show="okShow" @close="okShow = false" :round="10"
				mode="bottom">
				<view style="text-align: center;font-size: 32rpx;color: #3D3D3D;padding: 30rpx;font-weight: 600;">同意退款
				</view>
				<view style="display: flex;align-items: center;padding: 30rpx;">
					<view>
						<image style="width: 160rpx;height: 160rpx;border-radius: 18rpx;" :src="info.detail.images[0]"
							mode=""></image>
					</view>
					<view style="margin-left: 20rpx;">
						<view class="flex align-items">
							<text class="first-name">{{ info.detail.title }}</text>
						</view>
						<view class="first-image flex align-items space-between" style="width: 100%;margin-top: 30rpx;">
							<view class="flex" style="align-items: center;">
								<image src="/static/center/address.png" mode="" class="icon-size"></image>
								<text style="font-size: 26rpx;font-weight: 400;">{{ info.detail.address }}{{ info.detail.address_detail }}</text>
							</view>
						</view>
					</view>
				</view>
				<view style="padding: 0rpx 40rpx;">
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view style="font-size: 28rpx;font-weight: 600;">退款数量</view>
						<view>{{ info.auth_num }}</view>
					</view>
					<view style="height: 1px;background-color:#F0F0F0;width: 100%;margin: 30rpx 0rpx;"></view>
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view style="font-size: 28rpx;font-weight: 600;">支付金额</view>
						<view style="font-weight: 900;">￥{{ info.payprice }}</view>
					</view>
					<view style="height: 1px;background-color:#F0F0F0;width: 100%;margin: 30rpx 0rpx;"></view>
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view style="font-size: 28rpx;font-weight: 600;">退款金额</view>
						<view @click="openMoney" style="display: flex;justify-content: flex-end;align-items: center;">
							<view style="color: #FF4810;font-weight: 900;margin-right:20rpx;">
								￥{{ moeney }}
							</view>
							<u-icon name="edit-pen" size="28" color="#323232"></u-icon>
						</view>
					</view>
				</view>
				<view @click="SuccessDo('yes')" class="btn_1" style="margin-top: 60rpx;">确认退款</view>
			</u-popup>
			<u-popup @touchmove.native.stop.prevent :closeable="true" :show="noShow" @close="noShow = false" :round="10"
				mode="bottom">
				<view style="text-align: center;font-size: 32rpx;color: #3D3D3D;padding: 30rpx;font-weight: 600;">驳回退款
				</view>
				<view style="display: flex;align-items: center;padding: 30rpx;">
					<view>
						<image style="width: 160rpx;height: 160rpx;border-radius: 18rpx;" :src="info.detail.images[0]"
							mode=""></image>
					</view>
					<view style="margin-left: 20rpx;">
						<view class="flex align-items">
							<text class="first-name">{{ info.detail.title }}</text>
						</view>
						<view class="first-image flex align-items space-between" style="width: 100%;margin-top: 30rpx;">
							<view class="flex" style="align-items: center;">
								<image src="/static/center/address.png" mode="" class="icon-size"></image>
								<text>{{ info.detail.address }}{{ info.detail.address_detail }}</text>
							</view>
						</view>
					</view>
				</view>
				<view style="padding: 0rpx 40rpx;">
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view style="font-size: 28rpx;font-weight: 600;">退款数量</view>
						<view>{{ info.auth_num }}</view>
					</view>
					<view style="height: 1px;background-color:#F0F0F0;width: 100%;margin: 30rpx 0rpx;"></view>
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view style="font-size: 28rpx;font-weight: 600;">支付金额</view>
						<view style="font-weight: 900;">￥{{ info.payprice }}</view>
					</view>
					<view style="height: 1px;background-color:#F0F0F0;width: 100%;margin: 30rpx 0rpx;"></view>
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view style="font-size: 28rpx;font-weight: 600;">退款金额</view>
						<view style="display: flex;justify-content: flex-end;align-items: center;">
							<view style="color: #FF4810;font-weight: 900;">
								￥{{ moeney }}
							</view>
						</view>
					</view>
					<view>
						<view class="textarea_mph" style="margin-top: 30rpx;">
							<u--textarea maxlength="400" placeholder-class="bttops" v-model="reject_reason"
								placeholder="请输入驳回原因"></u--textarea>
						</view>
					</view>
				</view>
				<view @click="SuccessDo('no')" class="btn_1" style="margin-top: 60rpx;">确认驳回</view>
			</u-popup>
		</view>
	</view>
</template>

<script>
import dayjs from 'dayjs';
import {
	dateWeek
} from '../../utils/dateFormat'
export default {
	data() {
		return {
			noShow: false,
			okShow: false,
			id: '',
			info: {},
			moeney: 0,
			reject_reason: '',
		};

	},
	onLoad(op) {
		this.id = op.id;
		this.getInfo();
	},
	computed: {
		formattedTime() {
			const startTime = dateWeek(this.info.detail.start_time);
			const endTime = dateWeek(this.info.detail.end_time);
			return {
				formattedTime: `${startTime} ~ ${endTime}`
			};
		}
	},
	methods: {
		toDetail() {
			uni.navigateTo({
				url: '/packageA/center/detail?id=' + this.info.activity_id
			});
		},
		SuccessDo(type) {
			uni.$u.http.post('/api/school.newworker.activity.order/shop_confirmation', {
				order_no: this.id,
				status: type,
				price: this.moeney,
				reject_reason: this.reject_reason,

			}).then(res => {
				if (res.code == 1) {
					this.noShow = false;
					this.okShow = false;
					uni.showLoading({
						title: '处理中...'
					});
					setTimeout(() => {
						this.getInfo();
						uni.hideLoading();
					}, 2000)

				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
				}
			}).catch(error => { });
		},
		openMoney() {
			var that = this;
			uni.showModal({
				title: '请填写退款金额',
				placeholderText: '退款金额',
				content: this.moeney,
				editable: true,
				success: function (res) {
					console.log(res);
					if (res.confirm) {
						that.moeney = res.content;
					}
				}
			});
		},
		getInfo() {
			uni.$u.http.get('/api/school.newworker.activity.order/detail', {
				params: {
					id: this.id,
				}
			}).then(res => {
				if (res.code == 1) {
					this.info = res.data.detail;
					this.moeney = res.data.detail.first_refundprice;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
				}
			}).catch(error => { });
		},
		// 导航
		toMap(latitude, longitude, name) {
			uni.openLocation({
				latitude: parseFloat(latitude),
				longitude: parseFloat(longitude),
				name: name,
				success: function () {
					console.log('success');
				}
			});
		},
		formatTimestamp(timestamp) {
			console.log(timestamp)
			const date = new Date(timestamp * 1000); // 10位时间戳需要乘以1000
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
		},
		formatTimestampString(timestamp) {
			console.log(timestamp)
			const date = new Date(timestamp * 1000); // 10位时间戳需要乘以1000
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
		},

	}
}
</script>

<style lang="scss" scoped>
.detail_all {
	background-color: #f7f7f7;
}

.w-100 {
	width: 100%;
}

.flex {
	display: flex;
}

.flex-start {
	align-items: flex-start;
}

.justify-center {
	justify-content: center;
}

.align-items {
	align-items: center;
}

.flex-column {
	flex-flow: column;
}

.justify-start {
	justify-content: start;
}

.white-space {
	overflow: hidden;
	/* 确保超出容器的文本被隐藏 */
	white-space: nowrap;
	/* 确保文本在一行内显示 */
	text-overflow: ellipsis;
	/* 使用省略号表示被截断的文本 */
	width: 100%;
}

.con-center {
	// background: white;
	border-radius: 44rpx;
	position: relative;
	margin-top: 30rpx;

}



.space-between {
	justify-content: space-between;
}

.swiper {
	width: 100%;
	height: 580rpx;
}

.box {
	position: relative;
	margin: 0 30rpx;
}


.first-box {
	width: 690rpx;
	background: #FFFFFF;
	padding: 30rpx;
	border-radius: 18rpx;
	box-sizing: border-box;

	.head_img {
		width: 160rpx;
		height: 160rpx;
		border-radius: 18rpx;
	}

	.first-name {
		width: 440rpx;
		height: 39rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 600;
		font-size: 28rpx;
		color: #323232;
		// margin-left: 16rpx;
	}



	.first-image {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;

		text {
			// height: 80rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 26rpx;
			color: #222222;
		}

		.xieyi {
			background-color: #BBFC5B;
			width: 156rpx;
			height: 48rpx;
			border-radius: 8rpx;
		}
	}
}

.second-box {
	width: 690rpx;
	height: 64rpx;
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	margin: 20rpx 0 16rpx 0;


	.number {
		height: 40rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 26rpx;
		color: #323232;
		margin-left: 32rpx;
	}

	view {
		text {
			width: 24rpx;
			height: 24rpx;
			background: rgba(255, 255, 255, 0.4);
			border-radius: 24rpx;
			margin: 0 22rpx 0 4rpx;
		}
	}
}

.third {
	width: 100%;
	background: #ffffff;
	margin-top: 30rpx;
	border-radius: 44rpx;

	.third-top {
		font-family: PingFang SC, PingFang SC;
		font-weight: 700;
		font-size: 36rpx;
		color: #323232;
		line-height: 50rpx;
		margin-top: 30rpx;
		margin-left: 30rpx;
		position: relative;
		z-index: 10;

		view {
			position: relative;

			.icons {
				width: 37rpx;
				height: 20rpx;
				position: absolute;
				left: 0;
				bottom: 0;
				z-index: -1;
			}
		}
	}

	.third-center {
		padding: 30rpx;
		position: relative;

		.hui {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 26rpx;
			color: #9C9C9C;
		}

		.bottom {
			width: 100%;
			margin-bottom: 10rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 26rpx;
			color: #9C9C9C;
			text-align: center;
			font-style: normal;
			text-transform: none;
		}
	}



}

.line {
	width: 630rpx;
	height: 1rpx;
	background: #eeeeee;
	margin: 30rpx 0;
}

.icon-size {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
}


.footer {
	width: 100%;
	padding: 30rpx 30rpx 80rpx 30rpx;

	.footer-right {
		width: 50%;
		height: 90rpx;
		background: #323232;
		border-radius: 148rpx;

		text {
			font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 36rpx;
			color: #BBFC5B;
		}
	}

	.footer-right1 {
		width: 50%;
		height: 90rpx;
		background: #E4E4E4;
		border-radius: 148rpx;

		text {
			font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 36rpx;
			color: #9C9C9C;
		}
	}
}


.popupBox {
	width: 690rpx;
	height: 716rpx;

	.pop-header {
		width: 100%;
		background-image: url("/static/center/bg.png");
		background-repeat: no-repeat;
		background-position: left bottom;
		height: 265rpx;

		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 20rpx;
			color: #343434;
		}

		.name {
			width: 594rpx;
			height: 66rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			font-size: 44rpx;
			color: #222222;
			margin-top: 80rpx;
		}

		.price {
			width: 594rpx;
			height: 66rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			font-size: 44rpx;
			color: #FF4810;
			margin-top: 16rpx;

			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 36rpx;
				color: #FF2323;
				line-height: 32rpx;
			}
		}
	}

	.popup {
		display: flex;
		align-items: self-start;
		justify-content: center;
		width: 594rpx;
	}

	.popup-footer {
		position: absolute;
		left: 48rpx;
		bottom: 48rpx;

		text {
			width: 594rpx;
			height: 100rpx;
			background: #222222;
			border-radius: 200rpx 200rpx 200rpx 200rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			font-size: 32rpx;
			color: #BEEE03;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}


	.line {
		width: 642rpx;
		height: 1rpx;
		background: #eeeeee;
		//box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
		//border-radius: 0rpx 0rpx 0rpx 0rpx;
	}

	.times {
		width: 93%;

		.selectTime {
			width: 288rpx;
			height: 50rpx;
			background: #FFFFFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			border: 1rpx solid #D9D9D9;
			color: #4B4B4B;
			font-family: 'PingFang SC', 'PingFang SC';
			font-weight: 500;
			font-size: 24rpx;
			padding-left: 15rpx;
			cursor: pointer;
			margin: 24rpx 32rpx 0 0;
			white-space: nowrap;
			/* 防止文本换行 */
			text-overflow: ellipsis;
			/* 超出部分显示省略号 */
			overflow: hidden;
			/* 隐藏超出部分 */
			text-align: left;
			/* 文字靠左对齐 */
			line-height: 50rpx;
			/* 垂直居中对齐 */
			box-sizing: border-box;
			/* 确保 padding 和 border 不影响宽度和高度 */
			display: inline-block;
			/* 确保容器内文字正确对齐 */
		}

	}

	.selectTime.selected {
		width: 288rpx;
		height: 50rpx;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		background: #008CFF;
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 24rpx;
		color: #FFFFFF;
		cursor: pointer;
		margin: 24rpx 32rpx 0 0;

	}
}

.share {
	position: fixed;
	color: #FFFFFF;
	right: 0;
	bottom: 190rpx;
	background: linear-gradient(to bottom right, #FE726B, #FE956B);
	padding: 10rpx 10rpx 10rpx 20rpx;
	border-top-left-radius: 50px;
	border-bottom-left-radius: 50px;
	box-shadow: 0 0 20upx rgba(0, 0, 0, .09);
}

.cancel {
	width: 100vw;
	padding: 30rpx;
	text-align: center;
	background: #FFFFFF;
	color: red;
	font-weight: bold;
	font-size: 30rpx;
}

.md-content {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: 50rpx 0;
	background-color: white;
}

.md-content-item {
	margin: 0 70rpx;
	position: relative;
}

.md-content-item image {
	width: 100rpx;
	height: 100rpx;
}

.md-content-item view {
	margin-top: 15rpx;
	font-size: 28rpx;
}

.sharebtn {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0;
}

.cu-modal {
	position: fixed;
	bottom: 166rpx;
	left: 0;
	z-index: 999999;
}

.gj {
	.title {
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 32rpx;
		color: #4B4B4B;

		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #4B4B4B;
		}
	}

	.scroll {
		width: 642rpx;
		max-height: 340rpx;

		view {
			margin: 24rpx;
			width: 600rpx;
			height: 56rpx;
			background: #E8E8E8;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			.title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #7A7A7A;
			}

			.red {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #FF5F5F;
			}

			.lan {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #008CFF;
			}
		}
	}
}

::v-deep ::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 4px !important;
	height: 1px !important;
	overflow: auto !important;
	background: #ccc !important;
	-webkit-appearance: auto !important;
	display: block;
}

::v-deep ::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px !important;
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
	background: #7b7979 !important;
}

::v-deep ::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
	// border-radius: 10px !important;
	background: #FFFFFF !important;
}

.Poster {
	position: relative;
	top: 21rpx;
	left: 30rpx;
	width: 690rpx;
}

.posterClose {
	position: absolute;
	right: 8rpx;
	top: 8rpx;
}

.pos {
	position: relative;
}

.btnList {
	width: 690rpx;
	position: absolute;
	bottom: 150rpx;
	left: 30rpx;
	display: flex;
	justify-content: space-evenly;

	text {
		width: 250rpx;
		height: 80rpx;
		background: #FFFFFF;
		border-radius: 401rpx 401rpx 401rpx 401rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 34rpx;
		color: #008CFF;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.save {}
}

.no-scroll {
	overflow: hidden;
	height: 100vh;
}

::v-deep ._root {
	padding: 0 10rpx;
}

.no-border-button {
	background-color: transparent;
	/* 去掉背景色 */
	border: none;
	/* 去掉边框 */
	padding: 0;
	/* 去掉内边距 */
	margin: 0;
	/* 去掉外边距 */
	display: inline-flex;
	/* 使按钮内容居中 */
	align-items: center;
	/* 垂直居中 */
	justify-content: center;
	/* 水平居中 */
	flex-flow: column;
	height: 80rpx;
	line-height: inherit;

	text {
		width: 250rpx;
		height: 80rpx;
		background: #FFFFFF;
		border-radius: 401rpx 401rpx 401rpx 401rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 34rpx;
		color: #008CFF;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}

.btn_1 {
	width: 95%;
	height: 90rpx;
	background: #323232;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 36rpx;
	color: #BBFC5B;
	line-height: 90rpx;
	text-align: center;
	margin: 0 auto;
	margin-top: 30rpx;
}

.input {
	text-align: right;
	color: #FF4810;
	font-weight: 600;
}

.textarea_mph {
	::v-deep .u-textarea {
		height: 200rpx;
		padding: 20rpx;
		border: none;
		font-size: 26rpx;
		color: #9C9C9C;
		background-color: #F8F8F8 !important;
		border-radius: 18rpx;
	}
}

.refund-policy-table {
	width: 100%;

	.table-container {
		width: 100%;
		border: 1px solid #C1C1C1;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.table-header {
		display: flex;
		background-color: #E8E8E8;

		.th-item {
			flex: 1;
			text-align: center;
			font-size: 26rpx;
			color: #323232;
			padding: 24rpx 10rpx;
			border-right: 1px solid #C1C1C1;
			border-bottom: 1px solid #C1C1C1;

			&:first-child {
				flex: 2;
				/* 第一列宽度为其他列的2倍 */
			}

			&:last-child {
				border-right: none;
			}
		}
	}

	.table-body {
		.table-row {
			display: flex;
			border-top: 1px solid #C1C1C1;



			&:first-child {
				border-top: none;
			}

			.td-item {
				flex: 1;
				text-align: center;
				font-size: 26rpx;
				color: #323232;
				padding: 24rpx 10rpx;
				border-right: 1px solid #C1C1C1;
				background-color: #F7F7F7;

				&:first-child {
					flex: 2;
					/* 第一列宽度为其他列的2倍 */
				}

				&:last-child {
					border-right: none;
				}
			}
		}
	}
}
</style>