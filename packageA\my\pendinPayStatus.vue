<template>
	<view class="detail_all">
		<view class="nav">
			<u-navbar :is-back="true" leftIconColor="#323232" :autoBack='true' :title="toptitle + '订单'"
				bgColor="#FFFFFF" :titleStyle='titleStyle'> </u-navbar>
		</view>
		<view class="box flex justify-start flex-column align-items">

			<view class="con-center w-100 flex justify-center flex-column align-items flex-start">

				<view class="first-box flex flex-start flex-column justify-start">
					<view class="flex align-items justify-center" @click="openInfo()">
						<image class="head_img" v-if="detail.images!=[]" :src="detail.images[0]" mode=""></image>
						<view style="margin-left: 20rpx;">
							<view class="flex align-items">
								<span class="first-name" style="font-weight: 600;">{{detail.title}}</span>
							</view>
							<view class="first-image flex align-items" style="margin-top: 25rpx;">
								<image :src="detailAny.user.avatar" mode=""
									style="width: 36rpx;height: 36rpx;border-radius: 50%;"></image>
								<span class="white-space"
									style="color:#9C9C9C;margin-left: 10rpx;width: 210rpx;">{{ detailAny.user.nickname }}</span>
								<text style="color:#9C9C9C;margin-left: 20rpx;">/</text>
								<span
									style="color:#9C9C9C;margin-left: 20rpx;">{{ detailAny.join_info.people_number }}人玩过</span>

							</view>
							<view class="flex align-items" style="margin-top: 20rpx;">
								￥{{detailAny.totalprice}}
							</view>
						</view>
					</view>

					<span class="line"></span>
					<span class="first-image flex align-items" style="padding: 20rpx 0rpx;">
						<image src="../../static/center/Alarm.png" mode="" class="icon-size"></image>
						<span style="font-size: 26rpx;">{{formattedTime.formattedTime}}</span>
					</span>
					<span class="line"></span>

					<view class="first-image flex align-items space-between" style="width: 100%;margin-top: 10rpx;"
						@click.stop="toMap(detail.latitude, detail.longitude, detail.address_detail)">
						<view style="display: flex;">
							<view>
								<image src="/static/center/address.png" mode="" class="icon-size"></image>
							</view>
							<view class="white-space" style="font-size: 26rpx;color: #222222;font-weight: 400;">
								{{detail.address}}{{detail.address_detail}}
							</view>
						</view>
						<image src="/static/detail/daohang.png" class="icon-size" style="width: 50rpx; height: 50rpx;">
						</image>
					</view>

				</view>


			</view>
			<!-- 报名信息 -->
			<view class="signUpInfo">
				<view class="signInfo-top flex align-items">
					<view>报名信息
						<image class="icons" src="/static/detail/xiangqing.png"></image>
					</view>
				</view>
				<view style="margin-top: 10rpx;width: 630rpx;height: 240rpx;">
					<scroll-view class="signBoxList" scroll-y :show-scrollbar="false" :scroll-with-animation="true"
						enable-flex :style="{height: scrollViewHeight + 'rpx'}">
						<view class="sign-item" v-for="(item,index) in detailAny.ordercode" :key="index">
							<view style="font-size: 28rpx;font-weight: 400;line-height: 28rpx;color: #323232;">
								{{item.name}}
							</view>
							<view
								style="font-size: 24rpx;margin-top: 10rpx;font-weight: 400;color: #9c9c9c;line-height: 24rpx;">
								{{item.idnum}}
							</view>
						</view>

					</scroll-view>
				</view>



				<!-- <view class="signList">
						
					</view> -->
			</view>


			<view class="third flex flex-column" style="justify-content: flex-start;">
				<view class="third-center">
					<view class="flex w-100 space-between hui align-items" style="margin-top: 8rpx;">
						<span>订单状态</span>
						<span style="color: #323232;">{{ toptitle }}</span>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 30rpx;">
						<span>订单编号</span>
						<span style="color: #323232;">{{detailAny.order_no}}</span>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 30rpx;">
						<span>创建时间</span>
						<span style="color: #323232;">{{ formattime(detailAny.createtime)}}</span>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 30rpx;">
						<span>付款金额</span>
						<span style="color: #FF4810;">￥{{detailAny.totalprice}}</span>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 30rpx;">
						<span>付款方式</span>
						<span style="color: #323232;">{{detailAny.pay_type == 'wechat' ? '微信支付' : '余额'}}</span>
					</view>
					<view v-if="detailAny.paytime!=null " class="flex w-100 space-between hui align-items"
						style="margin-top: 30rpx;">
						<span>付款时间</span>
						<span style="color: #323232;">{{ formattime(detailAny.paytime)}}</span>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 30rpx;">
						<span>备注</span>
						<span style="color: #323232;">{{ detailAny.desc}}</span>
					</view>
				</view>

			</view>
			<view v-if="detailAny.server_status!=0" class="third flex flex-column"
				style="justify-content: flex-start;padding-bottom: 30rpx;">

				<view class="third-center" v-if="detailAny.auth_reason != '' && detailAny.auth_reason != null">
					<view class="flex w-100 space-between hui align-items">
						<text style="width: 400rpx;">退款原因</text>
						<text style="color: #323232;">{{ detailAny.auth_reason }}</text>
					</view>
				</view>

				<view class="third-center" v-if="detailAny.auth_status == 2" style="padding: 0rpx 30rpx;">
					<view class="flex w-100 space-between hui align-items" style="margin-top: 8rpx;">
						<text>是否同意退款</text>
						<text style="color: #FF4810;">否</text>
					</view>
					<view class="flex w-100 space-between hui" style="margin-top: 40rpx;">
						<text style="width: 400rpx;">驳回原因</text>
						<text style="color: #323232;">{{ detailAny.reason }}</text>
					</view>
				</view>

				<view class="third-center" v-if="detailAny.auth_status == 1" style="padding: 0rpx 30rpx;">
					<view class="flex w-100 space-between hui align-items" style="margin-top: 8rpx;">
						<text>是否同意退款</text>
						<text style="color: #323232;">是</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>退款数量</text>
						<text v-if="detailAny.before_status!=-3" style="color: #323232;">{{ detailAny.auth_num }}</text>
						<text v-if="detailAny.before_status==-3" style="color: #323232;">{{ detailAny.num }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>退款金额</text>
						<text v-if="detailAny.server_status == 3" style="color: #FF4810;font-weight: 600;">￥{{
								detailAny.first_refundprice }}</text>
						<text v-if="detailAny.server_status == 6" style="color: #FF4810;font-weight: 600;">￥{{
								detailAny.total_refundprice}}</text>
					</view>

				</view>
			</view>

			<view class="footer align-items flex-column" style="justify-content: space-between;">
				<view class="footer-right flex justify-center align-items" @click="pament()"
					v-if="detailAny.status == 0">
					<view class="spans">
						去支付 <u-count-down :time="detailAny.cancel_last_seconds*1000"></u-count-down>
					</view>
				</view>

				<view class="footer-right1 flex justify-center align-items" @click="nobuy()"
					v-if="detailAny.status == 0">
					<span class="spans"> 取消订单 </span>
				</view>
				<view @click="service(detailAny.id)" class="footer-right flex justify-center align-items"
					v-if="status == 9">
					<span> 申请售后 </span>
				</view>
				<view @click="toAddInvoic(detailAny.id)" class="footer-right flex justify-center align-items"
					v-if="detailAny.status == 9 && detailAny.invoice_status == 0 && detailAny.detail.feel == 0">
					<span> 开发票 </span>
				</view>
				<view @click="checkInvoice(detailAny.invoiceaplpy.id)"
					class="footer-right2 flex justify-center align-items"
					v-if="detailAny.status == 9 && detailAny.invoice_status == 2 && detailAny.detail.feel == 0">
					<span> 查看发票 </span>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs';
	import {
		dateWeek
	} from '../../utils/dateFormat'
	export default {
		computed: {
			scrollViewHeight() {
				// 根据内容数量计算合适的高度
				const baseHeight = 80; // 每项基础高度
				const minHeight = 100; // 最小高度
				const maxHeight = 240; // 最大高度
				const calculatedHeight = this.detailAny.ordercode.length * baseHeight;

				return Math.min(Math.max(calculatedHeight, minHeight), maxHeight);
			},
			formattedTimeList() {
				return this.timeList.map(item => {
					const startTime = dayjs.unix(item.start_time).format('YYYY-MM-DD HH:mm:ss');
					const endTime = dayjs.unix(item.end_time).format('HH:mm:ss');
					return {
						formattedTime: `${startTime}~${endTime}`,
						limit_num: item.limit_num,
						sign_num: item.sign_num
					};
				});
			},
			formattedTime() {
				const startTime = dateWeek(this.detail.start_time);
				const endTime = dateWeek(this.detail.end_time);
				return {
					formattedTime: `${startTime} ~ ${endTime}`
				};
			}
		},

		data() {
			return {
				style: {
					// 字符串的形式
					img: 'width: 100%'

				},
				value_slide: 0,
				scrollTop: 0,
				overlay: false,
				userInfo: {},
				path: 'https://testy.hschool.com.cn//uploads/20241219/3406baf51fcc28c63c31ebcee5c9c75e.jpg',
				uloadingShow: false,
				show: false,
				buyShow: false,
				type: 0, // 0 支付 1 立即购买 2 预约  3确认时间
				id: 1,
				count: 5,
				value: 5,
				order_no: '',
				PayPirce: 0,
				detail: {},
				detailAny: {},
				people: {},
				qrUrl: '',
				is_collect: 0,
				popupStyle: {
					width: '690rpx',
					height: '716rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'flex-start',
					alignItems: 'center'
				},
				timeList: [],
				selectedTime: null,
				// indexBackgroundImage: indexBackgroundImage,
				orderId: "",
				classes_lib_spec_id: '',
				order_no2: '',
				mobile: '',
				is_show_model: false, //是否显示分享模态窗
				background: '#ffffff00',
				titleStyle: {
					color: '#000000',
					fontSize: '32rpx',
					fontWeight: 'bold'
				},
				status: null,
				toptitle: '',
			};
		},



		onLoad(options) {
			this.userInfo = uni.getStorageSync("userInfo")
			this.id = options.id
			this.order_no = options.order_no
			this.status = options.status;
			if (options.status == 9) {
				this.toptitle = '已完成'
			} else if (options.status == -3) {
				this.toptitle = '已关闭'
			} else if (options.status == 4 || options.status == 7) {
				this.toptitle = '售后中'
			} else if (options.status == 5) {
				this.toptitle = '退款中'
			} else if (options.status == 6) {
				this.toptitle = '已退款'
			}
			this.getDetail()
		},

		methods: {
			openInfo() {
				uni.navigateTo({
					url: `/packageA/center/detail?id=${this.detail.activity_id}`
				})
			},
			// 支付
			pament() {
				let that = this
				that.uloadingShow = false
				uni.$u.http.post('/api/school.newactivity.pay/payment', {
					type: 'wechat',
					order_no: this.detailAny.order_no,
					platform: 'miniapp'
				}).then(res => {
					if (res.code == 1) {
						wx.requestPayment({
							timeStamp: res.data.paydata.timeStamp, //时间戳
							nonceStr: res.data.paydata.nonceStr, //随机字符串
							package: res.data.paydata.package, //prepay_id
							signType: res.data.paydata.signType, //签名算法MD5
							paySign: res.data.paydata.paySign, //签名
							success(res) {
								if (res.errMsg == "requestPayment:ok") {
									that.order_no = ''
									// uni.redirectTo({
									// 	url: "/packageA/my/exercise?status=" +'2,3'
									// })
									uni.navigateBack();
									console.log('支付成功', res)
								} else {
									that.uloadingShow = false
									console.log('支付失败')
								}
							},
							fail(res) {
								that.uloadingShow = false
								console.log('支付失败', res)
							}
						})
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			//取消订单
			nobuy() {
				let that = this;
				let o = this.detailAny.order_no
				uni.$u.http.post('/api/school.newactivity.order/freecancel', {
					order_no: o,
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: '取消成功',
							icon: 'success',
							duration: 2000,
							complete: function() {
								setTimeout(function() {
									uni.navigateBack()
									// uni.redirectTo({
									// 	url: "/packageA/my/exercise"
									// })
								}, 2000);
							}
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						that.uloadingShow = false

						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			//申请售后
			service(id) {
				uni.navigateTo({
					url: `/packageA/afterSales/index?id=${id}`
				})
			},
			//申请发票
			toAddInvoic(id, order_no) {
				const num = 1
				uni.navigateTo({
					url: '/packageB/invoice/addInvoice?id=' + id + "&order_nos=" + order_no + "&num=" + num
				})
			},
			//查看发票
			checkInvoice(id, order_no) {
				uni.navigateTo({
					url: '/packageB/invoice/invoiceInfo?id=' + id + "&order_no=" + order_no
				})
			},
			//星期
			dateWeeks(timestamp) {
				return dateWeek(timestamp)
			},
			//完整时间
			formattime(e) {
				return dayjs(e * 1000).format('YYYY-MM-DD HH:mm:ss')
			},

			overlayShow() {
				const token = uni.getStorageSync('token')
				if (token) {
					this.overlay = true
				} else {
					uni.showToast({
						title: '请登录',
						icon: 'none',
						duration: 2000,
						complete: function() {
							setTimeout(function() {
								uni.switchTab({
									url: '/pages/my/index',
								});
							}, 2000);
						}
					});
				}
			},








			// 提示
			showErrorToast(msg) {
				uni.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				});
			},
			// 获取详情
			getDetail() {
				uni.$u.http.get('/api/school.newactivity.order/detail', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail.detail;
						this.detailAny = res.data.detail;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			// 导航
			toMap(latitude, longitude, name) {
				// 修改页面标题为"导航"
				uni.setNavigationBarTitle({
					title: '导航',
					success: () => {
						console.log('导航栏标题修改成功');
						setTimeout(() => {
							uni.openLocation({
								latitude: parseFloat(latitude),
								longitude: parseFloat(longitude),
								name: name,
								success: function() {
									console.log('success');
								}
							});
						}, 300);
					},
					fail: (err) => {
						console.log('导航栏标题修改失败', err);
					}
				});

				// uni.openLocation({
				// 	latitude: parseFloat(latitude),
				// 	longitude: parseFloat(longitude),
				// 	name: name,
				// 	success: function() {
				// 		console.log('success');
				// 	}
				// });
			},

			// 返回首页
			toIndex() {
				uni.switchTab({
					url: "/pages/index/index"
				})
			},










		},
	}
</script>

<style lang="scss" scoped>
	/* 微信小程序特别处理 */
	/* #ifdef MP-WEIXIN */
	.signBoxList ::-webkit-scrollbar {
	  display: none !important;
	  width: 0 !important;
	  height: 0 !important;
	}
	/* #endif */

	.detail_all {
		background-color: #f7f7f7;
		height: 100vh;
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.grid {
		display: grid;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}

	.con-center {
		// background: white;
		border-radius: 44rpx;
		position: relative;
		margin-top: 30rpx;

	}



	.space-between {
		justify-content: space-between;
	}

	.swiper {
		width: 100%;
		height: 580rpx;
	}

	.box {
		position: relative;
		margin: 0 30rpx;
		margin-top: 180rpx;
	}


	.first-box {
		width: 690rpx;
		background: #FFFFFF;
		padding: 30rpx;
		border-radius: 44rpx;
		box-sizing: border-box;

		.head_img {
			width: 160rpx;
			height: 160rpx;
			border-radius: 18rpx;
		}

		.first-name {
			width: 440rpx;
			height: 39rpx auto;
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			font-size: 28rpx;
			color: #323232;
			// margin-left: 16rpx;
		}



		.first-image {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;

			span {
				// height: 80rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #222222;
			}

			.xieyi {
				background-color: #BBFC5B;
				width: 156rpx;
				height: 48rpx;
				border-radius: 8rpx;
			}
		}
	}

	.second-box {
		width: 690rpx;
		height: 64rpx;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		margin: 20rpx 0 16rpx 0;


		.number {
			height: 40rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 26rpx;
			color: #323232;
			margin-left: 32rpx;
		}

		view {
			span {
				width: 24rpx;
				height: 24rpx;
				background: rgba(255, 255, 255, 0.4);
				border-radius: 24rpx;
				margin: 0 22rpx 0 4rpx;
			}
		}
	}

	.third {
		width: 100%;
		background: #ffffff;
		margin-top: 30rpx;
		border-radius: 44rpx;

		.third-center {
			padding: 30rpx;

			.hui {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #7A7A7A;
			}
		}



	}

	.signUpInfo {
		width: 690rpx;
		height: 366rpx;
		padding: 30rpx;
		margin-top: 30rpx;
		box-sizing: border-box;
		background-color: #ffffff;
		border-radius: 44rpx;

		.signInfo-top {
			font-family: PingFang SC, PingFang SC;
			font-weight: 700;
			font-size: 36rpx;
			color: #323232;
			line-height: 50rpx;
			// margin-top: 30rpx;
			// margin-left: 30rpx;
			position: relative;
			z-index: 10;

			view {
				position: relative;

				.icons {
					width: 37rpx;
					height: 20rpx;
					position: absolute;
					left: 0;
					bottom: 0;
					z-index: -1;
				}
			}

		}

		.signBoxList {
			display: grid;
			justify-content: flex-start;
			align-items: center;
			border: none !important;
			box-shadow: none !important;
			width: 630rpx;
			// height: 236rpx;
			padding-right: 0 !important;
			overflow: hidden;
			overflow-y: auto;
			-webkit-overflow-scrolling: touch;
			/* 平滑滚动 */
			scrollbar-width: none;
			/* Firefox */
			-ms-overflow-style: none;

			/* 通用隐藏方法 */
			::-webkit-scrollbar {
				display: none;
				width: 0;
				height: 0;
				color: transparent;
			}

			.sign-item {
				width: 630rpx;
				margin-top: 20rpx;
				margin-bottom: 20rpx;
			}



		}



	}

	.line {
		width: 630rpx;
		height: 1rpx;
		background: #F0F0F0;
		margin: 19rpx 0;
	}

	.icon-size {
		width: 32rpx;
		height: 32rpx;
		margin-right: 12rpx;
	}


	.footer {
		width: 100%;
		height: 166rpx;
		margin-top: 50rpx;
		display: grid;

		.footer-right {
			width: 690rpx;
			height: 90rpx;
			background: #323232;
			border-radius: 148rpx;
			margin-bottom: 30rpx;

			span {
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 36rpx;
				color: #BBFC5B;
			}

			.spans {
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 36rpx;
				color: #BBFC5B;
				line-height: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.u-count-down__text {
				color: #BBFC5B !important;
			}
		}

		.footer-right2 {
			width: 690rpx;
			height: 90rpx;
			background: #f5f5f5;
			border-radius: 148rpx;
			border: 2rpx solid #323232;

			span {
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 36rpx;
				color: #9C9C9C;
			}
		}

		.footer-right1 {
			width: 690rpx;
			height: 90rpx;
			background: #E4E4E4;
			border-radius: 148rpx;

			span {
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 36rpx;
				color: #9C9C9C;
			}

			.spans {
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 36rpx;
				color: #9C9C9C;
			}
		}
	}


	.popupBox {
		width: 690rpx;
		height: 716rpx;

		.pop-header {
			width: 100%;
			background-image: url("/static/center/bg.png");
			background-repeat: no-repeat;
			background-position: left bottom;
			height: 265rpx;

			span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 20rpx;
				color: #343434;
			}

			.name {
				width: 594rpx;
				height: 66rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 44rpx;
				color: #222222;
				margin-top: 80rpx;
			}

			.price {
				width: 594rpx;
				height: 66rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 44rpx;
				color: #FF4810;
				margin-top: 16rpx;

				span {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 36rpx;
					color: #FF2323;
					line-height: 32rpx;
				}
			}
		}

		.popup {
			display: flex;
			align-items: self-start;
			justify-content: center;
			width: 594rpx;
		}

		.popup-footer {
			position: absolute;
			left: 48rpx;
			bottom: 48rpx;

			span {
				width: 594rpx;
				height: 100rpx;
				background: #222222;
				border-radius: 200rpx 200rpx 200rpx 200rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 32rpx;
				color: #BEEE03;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}


		.line {
			width: 642rpx;
			height: 1rpx;
			background: #D9D9D9;
			box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}

		.times {
			width: 93%;

			.selectTime {
				width: 288rpx;
				height: 50rpx;
				background: #FFFFFF;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				border: 1rpx solid #D9D9D9;
				color: #4B4B4B;
				font-family: 'PingFang SC', 'PingFang SC';
				font-weight: 500;
				font-size: 24rpx;
				padding-left: 15rpx;
				cursor: pointer;
				margin: 24rpx 32rpx 0 0;
				white-space: nowrap;
				/* 防止文本换行 */
				text-overflow: ellipsis;
				/* 超出部分显示省略号 */
				overflow: hidden;
				/* 隐藏超出部分 */
				text-align: left;
				/* 文字靠左对齐 */
				line-height: 50rpx;
				/* 垂直居中对齐 */
				box-sizing: border-box;
				/* 确保 padding 和 border 不影响宽度和高度 */
				display: inline-block;
				/* 确保容器内文字正确对齐 */
			}

		}

		.selectTime.selected {
			width: 288rpx;
			height: 50rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			background: #008CFF;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #FFFFFF;
			cursor: pointer;
			margin: 24rpx 32rpx 0 0;

		}
	}

	.share {
		position: fixed;
		color: #FFFFFF;
		right: 0;
		bottom: 190rpx;
		background: linear-gradient(to bottom right, #FE726B, #FE956B);
		padding: 10rpx 10rpx 10rpx 20rpx;
		border-top-left-radius: 50px;
		border-bottom-left-radius: 50px;
		box-shadow: 0 0 20upx rgba(0, 0, 0, .09);
	}

	.cancel {
		width: 100vw;
		padding: 30rpx;
		text-align: center;
		background: #FFFFFF;
		color: red;
		font-weight: bold;
		font-size: 30rpx;
	}

	.md-content {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;
		background-color: white;
	}

	.md-content-item {
		margin: 0 70rpx;
		position: relative;
	}

	.md-content-item image {
		width: 100rpx;
		height: 100rpx;
	}

	.md-content-item view {
		margin-top: 15rpx;
		font-size: 28rpx;
	}

	.sharebtn {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		opacity: 0;
	}

	.cu-modal {
		position: fixed;
		bottom: 166rpx;
		left: 0;
		z-index: 999999;
	}

	.gj {
		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 32rpx;
			color: #4B4B4B;

			span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #4B4B4B;
			}
		}

		.scroll {
			width: 642rpx;
			max-height: 340rpx;

			view {
				margin: 24rpx;
				width: 600rpx;
				height: 56rpx;
				background: #E8E8E8;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #7A7A7A;
				}

				.red {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #FF5F5F;
				}

				.lan {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #008CFF;
				}
			}
		}
	}

	::v-deep ::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 4px !important;
		height: 1px !important;
		overflow: auto !important;
		background: #ccc !important;
		-webkit-appearance: auto !important;
		display: block;
	}

	::v-deep ::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 10px !important;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		background: #7b7979 !important;
	}

	::v-deep ::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		// border-radius: 10px !important;
		background: #FFFFFF !important;
	}

	.Poster {
		position: relative;
		top: 21rpx;
		left: 30rpx;
		width: 690rpx;
	}

	.posterClose {
		position: absolute;
		right: 8rpx;
		top: 8rpx;
	}

	.pos {
		position: relative;
	}

	.btnList {
		width: 690rpx;
		position: absolute;
		bottom: 150rpx;
		left: 30rpx;
		display: flex;
		justify-content: space-evenly;

		span {
			width: 250rpx;
			height: 80rpx;
			background: #FFFFFF;
			border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #008CFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.save {}
	}

	.no-scroll {
		overflow: hidden;
		height: 100vh;
	}

	::v-deep ._root {
		padding: 0 10rpx;
	}

	.no-border-button {
		background-color: transparent;
		/* 去掉背景色 */
		border: none;
		/* 去掉边框 */
		padding: 0;
		/* 去掉内边距 */
		margin: 0;
		/* 去掉外边距 */
		display: inline-flex;
		/* 使按钮内容居中 */
		align-items: center;
		/* 垂直居中 */
		justify-content: center;
		/* 水平居中 */
		flex-flow: column;
		height: 80rpx;
		line-height: inherit;

		span {
			width: 250rpx;
			height: 80rpx;
			background: #FFFFFF;
			border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #008CFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
</style>
<style lang="scss">
	.footer {

		// #ifdef MP-WEIXIN
		.u-count-down__text {
			color: #bbfc5b !important;
			margin-left: 20rpx;
		}

		// #endif
	}


	.value_slide {
		width: 50%;

		::v-deep .uni-slider-handle-wrapper {
			height: 10rpx;
		}

		::v-deep .uni-slider-handle {
			background: url('@/static/detail/qiu.png') !important;
			border-radius: 0;
			background-size: 36rpx 36rpx !important;
			width: 36rpx;
			height: 36rpx;
			top: 14rpx;
			margin-left: -18rpx !important;
		}

		::v-deep .uni-slider-value {
			color: #323232;

			&::after {
				content: '%';
			}
		}

		// #ifdef MP-WEIXIN
		.wx-slider-handle-wrapper {
			height: 8rpx;
		}

		.wx-slider-handle {
			background: url('@/static/detail/qiu.png') !important;
			border-radius: 0;
			background-size: 28rpx 28rpx !important;
			width: 28rpx;
			height: 28rpx;
			top: 18rpx;
			margin-left: -14rpx !important;
		}

		.wx-slider-value {
			display: flex;
			width: 30rpx;
			color: #323232;

			&::after {
				content: '%';
			}
		}

		// #endif
	}
</style>