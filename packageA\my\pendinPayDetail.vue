<template>
	<view class="detail_all">
		<view class="box flex justify-start flex-column align-items">

			<view class="con-center w-100 flex justify-center flex-column align-items flex-start">

				<view class="first-box flex flex-start flex-column justify-start">
					<view class="flex align-items justify-center" @click="toInfo(detail.activity_id)">
						<image class="head_img" v-if="detail.images.length > 0" :src="detail.images[0]" mode=""></image>
						<view style="margin-left: 20rpx;">
							<view class="flex align-items">
								<text class="first-name" style="font-weight: 600;">{{ detail.title }}</text>
							</view>
							<view class="first-image flex align-items" style="margin-top: 25rpx;">
								<image :src="detailAny.user.avatar" mode=""
									style="width: 36rpx;height: 36rpx;border-radius:50%;"></image>
								<text class="white-space"
									style="color:#9C9C9C;margin-left: 10rpx;width: 210rpx;">{{ detailAny.user.nickname }}</text>
								<text style="color:#9C9C9C;margin-left: 20rpx;">/</text>
								<text style="color:#9C9C9C;margin-left: 20rpx;">{{ detailAny.join_info.people_number
									}}人玩过</text>
							</view>
							<view class="flex align-items" style="margin-top: 20rpx;">
								￥{{ detailAny.totalprice }}
							</view>
						</view>
					</view>

					<text class="line"></text>
					<view class="first-image flex align-items">
						<image src="../../static/center/Alarm.png" mode="" class="icon-size"></image>
						<text>{{ formattedTime.formattedTime }}</text>
					</view>
					<text class="line"></text>

					<view class="first-image flex align-items space-between" style="width: 100%;"
						@click.stop="toMap(detail.latitude, detail.longitude, detail.address_detail)">
						<view class="flex">
							<image src="/static/center/address.png" mode="" class="icon-size"></image>
							<text>{{ detail.address }}{{ detail.address_detail }}</text>
						</view>
						<image src="/static/detail/daohang.png" class="icon-size" style="width: 48rpx; height: 48rpx;">
						</image>
					</view>

				</view>


			</view>
			<view class="third flex flex-column" style="justify-content: flex-start;">
				<view class="third-top flex align-items">
					<view>核销二维码
						<image class="icons" src="/static/detail/xiangqing.png"></image>
					</view>
				</view>
				<view class="thirdCrode-center">
					<qrcode-swiper :images="qrimages" @onSwiperChanges="onSwiperChanges"> </qrcode-swiper>
					<!-- <l-painter v-for="qrcodesurl in detailAny.ordercode">
					<l-painter-view css="margin-top: 20rpx;margin-bottom: 20rpx;">
						<l-painter-qrcode css="width: 200rpx; height: 200rpx;margin:0 auto;" :text="qrcodesurl.miniurl"></l-painter-qrcode>
					</l-painter-view>
				</l-painter> -->
					<view class="bottom">
						<text>二维码核销（{{ qrindex }}/{{ detailAny.num }}）张</text>
					</view>
				</view>

			</view>

			<view class="third flex flex-column" style="justify-content: flex-start;">
				<view class="third-center">
					<view class="flex w-100 space-between hui align-items" style="margin-top: 8rpx;">
						<text>订单状态</text>
						<text style="color: #323232;"
							v-if="detailAny.status == 2 || detailAny.status == 3 || detailAny.status == 4">待核销</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>订单编号</text>
						<text style="color: #323232;">{{ detailAny.order_no }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>创建时间</text>
						<text style="color: #323232;">{{ formattime(detailAny.createtime) }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>付款金额</text>
						<text style="color: #FF4810;font-weight: 600;">￥{{ detailAny.totalprice }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>付款方式</text>
						<text style="color: #323232;">{{ detailAny.pay_type == 'wechat' ? '微信支付' : '余额' }}</text>
					</view>
					<view class="flex w-100 space-between hui align-items" style="margin-top: 40rpx;">
						<text>付款时间</text>
						<text style="color: #323232;">{{ formattime(detailAny.paytime) }}</text>

					</view>
					<view class="flex w-100 space-between hui align-items"
						style="margin-top: 30rpx;">
						<text>备注</text>
						<text style="color: #323232;">{{ detailAny.desc}}</text>
					</view>
				</view>
			</view>

			<!-- v-if="detailAny.feel == 0" -->
			<view class="footer flex align-items flex-column" style="justify-content: space-between;">
				<view @click="service(detailAny.id)" class="footer-right flex justify-center align-items"
					v-if="detailAny.detail.feel == 0 && (detailAny.status == 9 || detailAny.status == 2 || detailAny.status == 3) && (detail.status==4|| detail.status==5)">
					<text> 申请售后 </text>
				</view>
				<view @click="openCancels" class="footer-right flex justify-center align-items"
					v-if="detailAny.detail.feel == 0 && detailAny.status == 2 && (detail.status==1 || detail.status==2 || detail.status==3)">
					<text>取消订单</text>
				</view>
				<view @click="openCancels" class="footer-right flex justify-center align-items"
					v-if="detailAny.detail.feel == 1 && detailAny.status == 2 && (detail.status==1 || detail.status==2 || detail.status==3)">
					<text>取消订单</text>
				</view>
			</view>

			<!-- 二次确认弹窗 -->
			<u-popup :show="cancelsShow" mode="center" :round="10" :zIndex="99999" :custom-style="popupStyleOk"
				:safeAreaInsetBottom="false" :closeable="false">
				<view class="popupBox flex justify-start align-items flex-column">
					<view class="pop-header flex align-items flex-column flex-start">
						<span class="name">请确认是否取消报名</span>
						<span class="price">
							若频繁取消，可能会影响账号部分功能使用。建议您确认行程后再操作。
						</span>
					</view>
					<view
						style="display: flex;align-items: center;justify-content: space-between;width: 89%;gap: 30rpx;padding-top: 30rpx;">
						<view @click="closeCancels" class="btn_2">关闭</view>
						<view @click="paidcancel(detailAny.id)"
							v-if="detailAny.detail.feel == 1 && detailAny.status == 2 && (detail.status==1 || detail.status==2 || detail.status==3)"
							class="btn_1">确认取消</view>
						<view @click="cancelOrder(detailAny.id)"
							v-if="detailAny.detail.feel == 0 && detailAny.status == 2 && (detail.status==1 || detail.status==2 || detail.status==3)"
							class="btn_1">确认取消</view>
						<!-- <image src="../../static/center/price.png" mode="" style="width: 642rpx;height: 80rpx;"></image> -->
						<!-- <u-loading-icon :vertical="true" v-if="uloadingShow"></u-loading-icon> -->
					</view>
				</view>
			</u-popup>

		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs';
	import qrcodeSwiper from '@/packageA/qrcodeSwiper.vue'
	import {
		dateWeek
	} from '../../utils/dateFormat'
	export default {
		components: {
			qrcodeSwiper
		},
		computed: {
			// formattedTitle() {
			// 	if (this.detail.title.length > 9) {
			// 		return this.detail.title.slice(0, 9) + '..';
			// 	}
			// 	return this.detail.title;
			// },
			formattedTimeList() {
				return this.timeList.map(item => {
					const startTime = dayjs.unix(item.start_time).format('YYYY-MM-DD HH:mm:ss');
					const endTime = dayjs.unix(item.end_time).format('HH:mm:ss');
					return {
						formattedTime: `${startTime}~${endTime}`,
						limit_num: item.limit_num,
						sign_num: item.sign_num
					};
				});
			},
			formattedTime() {
				const startTime = dateWeek(this.detail.start_time);
				const endTime = dateWeek(this.detail.end_time);
				return {
					formattedTime: `${startTime} ~ ${endTime}`
				};
			}
		},

		data() {
			return {

				style: {
					// 字符串的形式
					img: 'width: 100%'

				},
				qrindex: 1,
				scrollTop: 0,
				overlay: false,
				userInfo: {},
				path: 'https://testy.hschool.com.cn//uploads/20241219/3406baf51fcc28c63c31ebcee5c9c75e.jpg',
				uloadingShow: false,
				show: false,
				buyShow: false,
				type: 0, // 0 支付 1 立即购买 2 预约  3确认时间
				id: 1,
				count: 5,
				value: 5,
				order_no: '',
				PayPirce: 0,
				detail: {},
				detailAny: {},
				people: {},
				qrUrl: '',
				is_collect: 0,
				popupStyleOk: {
					width: '640rpx',
					height: '300rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'flex-start',
					alignItems: 'center',
					borderRadius: '50rpx'
				},
				popupStyle: {
					width: '690rpx',
					height: '716rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'flex-start',
					alignItems: 'center'
				},
				timeList: [],
				selectedTime: null,
				// indexBackgroundImage: indexBackgroundImage,
				orderId: "",
				classes_lib_spec_id: '',
				order_no2: '',
				mobile: '',
				is_show_model: false, //是否显示分享模态窗
				background: '#ffffff00',
				titleStyle: {
					color: '#FFFFFF'
				},
				// qrList:[],//二维码地址列表
				//二维码图片列表
				qrimages: [],
				cancelsShow: false,
			};
		},

		onShareTimeline() {
			return {
				title: this.detail.title, //分享的标题
				imageUrl: this.detail.headimage, //展示的图片，这里是本地路径的写法，也可以写http或https开头的图片路径
				query: `id=${this.id}`
			}
		},
		onLoad(options) {
			this.userInfo = uni.getStorageSync("userInfo")
			this.id = options.id
			// this.id = 1
			if (options.type == 2) {
				this.type = 2
				this.orderId = options.orderId
			}
			if (options.type == 1) {
				this.type = 1
				this.order_no = options.order_no
				this.pament()
			}
			this.order_no = options.order_no
			console.log(options.id)
			this.getDetail()
			this.getShare();
			//轮询访问this.getDetail()
			setTimeout(() => {
				setInterval(() => {
					this.getDetail()
				}, 10000)
			}, 10000)
		},

		methods: {
			openCancels() {
				this.cancelsShow = true;
			},
			closeCancels() {
				this.cancelsShow = false;
			},
			toInfo(id) {
				uni.navigateTo({
					url: "/packageA/center/detail?id=" + id
				})
			},
			// 取消订单 (1)  免费活动
			paidcancel() {
				uni.showLoading({
					title: '处理中...'
				});
				this.cancelsShow = false;
				uni.$u.http.post('/api/school.newactivity.order/freecancel', {
					order_no: this.order_no
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
						this.getDetail()
						// uni.navigateBack(1)
						// uni.navigateBack({
						// 	delta: 1,
						// 	success: (event) => {
						// 		const pages = getCurrentPages()
						// 		let prevPage = pages[pages.length - 1]
						// 		prevPage.onLoad(prevPage.options)
						// 	}
						// });
						// setTimeout(() => {
						// 	uni.hideLoading();
						// }, 2000)

					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
						//uni.hideLoading();
					}
				}).catch(error => {
					uni.showToast({
						title: error.msg,
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 取消订单 (0) 付费
			cancelOrder() {
				this.cancelsShow = false;
				uni.$u.http.post('/api/school.newactivity.order/paidcancel', {
					order_no: this.order_no
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
						this.getDetail()
						// uni.navigateBack(1)
						// uni.navigateBack({
						// 	delta: 1,
						// 	success: (event) => {
						// 		const pages = getCurrentPages()
						// 		let prevPage = pages[pages.length - 1]
						// 		prevPage.onLoad(prevPage.options)
						// 	}
						// });
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},
			// 批量生成二维码
			// generateAllQRCodes() {
			// 	this.qrList.forEach((item, index) => {
			// 		this.generateQRCode(item.miniurl, index);
			// 	});
			// },
			// 单张生成逻辑
			// generateQRCode(text, index) {
			// 	uQRCode.make({
			// 	canvasId: 'qrcode1',
			// 	text: text,
			// 	size: 200,
			// 	backgroundColor: '#ffffff',
			// 	foregroundColor: '#000000',
			// 	success: (res) => {
			// 		console.log(res)
			// 		debugger
			// 		// 将 Canvas 转为临时图片路径
			// 		wx.canvasToTempFilePath({
			// 			canvasId: 'qrcode1',
			// 			success: (res) => {
			// 				console.log(res)
			// 				const tempPath = res.tempFilePath;
			// 				const qrList = this.qrList;
			// 				qrList[index].imgPath = tempPath;
			// 				this.setData({ qrLists });
			// 				this.qrimages.push(tempPath);
			// 			},
			// 			fail: (err) => console.error('生成失败:', err)
			// 		});
			// 	}
			// 	});
			// },

			//星期
			dateWeeks(timestamp) {
				return dateWeek(timestamp)
			},
			//完整时间
			formattime(e) {
				return dayjs(e * 1000).format('YYYY-MM-DD HH:mm:ss')
			},
			onSwiperChanges(e) {
				this.qrindex = e + 1
			},
			overlayShow() {
				const token = uni.getStorageSync('token')
				if (token) {
					this.overlay = true
				} else {
					uni.showToast({
						title: '请登录',
						icon: 'none',
						duration: 2000,
						complete: function() {
							setTimeout(function() {
								uni.switchTab({
									url: '/pages/my/index',
								});
							}, 2000);
						}
					});
				}
			},
			getShare() {
				uni.$u.http.post('/api/wechat_util/link', {
					path: 'packageA/center/detail',
					query: `id=${this.id}`,
				}).then(res => {
					if (res.code == 1) {
						this.qrUrl = res.data.url_link
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 时间转换函数
			timeago(timestamp) {
				const now = new Date().getTime(); // 当前时间（毫秒）
				const diff = (now - timestamp * 1000) / 1000; // 时间差（秒）

				if (diff < 60) {
					return `${Math.floor(diff)}秒前`;
				} else if (diff < 3600) {
					return `${Math.floor(diff / 60)}分钟前`;
				} else if (diff < 86400) {
					return `${Math.floor(diff / 3600)}小时前`;
				} else if (diff < 2592000) { // 30天
					return `${Math.floor(diff / 86400)}天前`;
				} else {
					return `${Math.floor(diff / 2592000)}个月前`;
				}
			},

			// sharePoster() {
			// 	//获取带参数二维码并传递
			// 	this.is_show_model = false
			// 	this.$refs.poster.showCanvas()
			// },

			// 提示
			showErrorToast(msg) {
				uni.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				});
			},
			// 获取详情
			getDetail() {
				uni.$u.http.get('/api/school.newactivity.order/detail', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail.detail;

						// this.qrList = res.data.detail.ordercode;
						this.qrimages = res.data.detail.ordercode;
						this.detailAny = res.data.detail;
						console.log('{{detailAny.status}}', detailAny.status, detailAny);
						this.generateAllQRCodes()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			// 报名  0 支付 1 立即购买 2 预约  3确认时间

			sign() {

				// this.buyShow = true
				// this.type = 3;

				uni.navigateTo({
					url: '/packageA/center/applyDetail?id=' + this.id
				})

			},
			buy() {
				this.uloadingShow = true
				this.getMoney()
			},
			// 购买弹窗 type =  0 支付 1 立即购买 2 预约  3确认时间
			buyOpen() {
				this.buyShow = true
				this.type = 1
			},
			// 预约弹窗 type = 2 0 支付 1 立即购买 2 预约  3确认时间
			open() {
				this.show = true
				this.type = 3
			},
			// 确认时间  type = 0  0 支付 1 立即购买 2 预约  3确认时间
			confimTime() {
				uni.$u.http.post('/api/school/hour_order/confirm', {
					classes_order_id: this.orderId,
					classes_lib_spec_id: this.classes_lib_spec_id,
					order_no: this.order_no2,
					is_compute: 1
				}).then(res => {
					if (res.code == 1) {
						this.order_no2 = res.data.order_no
						this.timeCreat(res.data.order_no)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						this.type = 2
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});

				this.show = false
				this.type = 0
			},
			// 预约下单
			timeCreat(order_no) {
				uni.$u.http.post('/api/school/hour_order/create', {
					order_no: order_no,
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000,
							complete: function() {
								setTimeout(function() {
									uni.reLaunch({
										url: "/packageA/my/makeList?status=" + -1
									})
								}, 2000);
							}

						})

					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						this.type = 2
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			// 教师详情
			toTeacher(id) {
				uni.navigateTo({
					url: `/packageA/center/teacherDetail?id=${id}`
				})
			},
			// 导航
			toMap(latitude, longitude, name) {
				uni.openLocation({
					latitude: parseFloat(latitude),
					longitude: parseFloat(longitude),
					name: name,
					success: function() {
						console.log('success');
					}
				});
			},


			close() {
				this.type = 0
				this.selectedTime = null
				this.show = false
			},
			buyClose() {
				this.type = 0
				this.selectedTime = null
				this.buyShow = false
			},
			// 返回首页
			toIndex() {
				uni.switchTab({
					url: "/pages/index/index"
				})
			},
			// 保存海报
			save() {
				let base64 = this.path.replace(/^data:image\/\w+;base64,/, ""); //图片替换
				let filePath = wx.env.USER_DATA_PATH + '/qrcode.png';
				uni.getFileSystemManager().writeFile({
					filePath: filePath, //创建一个临时文件名
					data: base64, //写入的文本或二进制数据
					encoding: 'base64', //写入当前文件的字符编码
					success: (res) => {
						uni.saveImageToPhotosAlbum({
							filePath: filePath,
							success: () => {
								uni.showToast({
									title: '保存成功',
									icon: "none",
									duration: 5000
								})
							},
							fail: (err) => {
								console.log(err);
								uni.showToast({
									title: '保存失败',
									icon: "none",
									duration: 5000
								})
							}
						})
					},
					fail: (err) => {
						console.log(err)
					}
				})
			},
			//分享发布
			sharePoster() { //分享图片给好友按钮的点击事件函数
				let that = this
				this.base64ToFilePath(this.path, (filePath) => {
					console.log(filePath);
					wx.showShareImageMenu({ //分享给朋友
						path: filePath,
						success: (res) => {
							console.log("分享成功：", res);
						},
						fail: (err) => {
							console.log("分享取消：", err);
						},
					})
				})
			},


			base64ToFilePath(base64data, callback) {
				const time = new Date().getTime();
				const imgPath = `${wx.env.USER_DATA_PATH}/addFriends${time}share_qrcode.png`;
				const imageData = base64data.replace(/^data:image\/\w+;base64,/, "");
				const fileSystemManager = uni.getFileSystemManager();

				fileSystemManager.writeFile({
					filePath: imgPath,
					data: imageData,
					encoding: 'base64',
					success: () => {
						callback(imgPath);
					},
					fail: (err) => {
						console.error('Write file failed:', err);
						uni.showToast({
							title: '写入文件失败',
							icon: 'none'
						});
					}
				});
			},

			// 收藏和取消
			Collect(number) {
				uni.$u.http.post('/api/school/classes/collect', {
					id: this.id,
					is_collect: number
				}).then(res => {
					if (res.code == 1) {
						this.is_collect = number
						if (number == 0) {
							uni.showToast({
								title: '取消收藏',
								icon: 'none',
								duration: 2000
							})
						} else {
							uni.showToast({
								title: '收藏成功',
								icon: 'none',
								duration: 2000
							})
						}

					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			},
			//申请售后
			service(id) {
				uni.navigateTo({
					url: `/packageA/afterSales/index?id=${id}`
				})
			},
			// 取消选择
			cancel() {
				this.selectedTime = null
			},
			selectTime(time) {
				this.selectedTime = time;
				this.classes_lib_spec_id = time.id

			},
			timeSelected(time) {
				return this.selectedTime === time;
			},
			moveScroll() {},
			// 获取价格
			getMoney() {
				uni.$u.http.post('/api/school.newactivity.order/confirm', {
					activity_id: this.id,
					order_no: this.order_no,
					is_compute: 1,
					num: 1
				}).then(res => {
					if (res.code == 1) {
						this.PayPirce = res.data.order_data.totalprice
						this.order_no = res.data.order_no
						this.create(this.order_no, this.PayPirce)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
					this.uloadingShow = false
				}).catch(error => {

				});
			},
			// 创建订单
			create(order_no, PayPirce) {
				uni.$u.http.post('/api/school.newactivity.order/create', {
					order_no: order_no,
				}).then(res => {
					if (res.code == 1) {
						if (PayPirce != 0) {
							this.pament()
						} else {
							this.uloadingShow = false
							uni.showToast({
								title: '创建成功',
								icon: 'success',
								duration: 2000,
								complete: function() {
									setTimeout(function() {
										uni.redirectTo({
											url: "/packageA/my/orderList?status=" + 3
										})
									}, 2000);
								}
							});
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						that.uloadingShow = false

						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			// 支付
			pament() {
				let that = this
				that.uloadingShow = false
				uni.$u.http.post('/api/school.newactivity.pay/payment', {
					type: 'wechat',
					order_no: that.order_no,
					platform: 'miniapp'
				}).then(res => {
					if (res.code == 1) {
						wx.requestPayment({
							timeStamp: res.data.paydata.timeStamp, //时间戳
							nonceStr: res.data.paydata.nonceStr, //随机字符串
							package: res.data.paydata.package, //prepay_id
							signType: res.data.paydata.signType, //签名算法MD5
							paySign: res.data.paydata.paySign, //签名
							success(res) {
								if (res.errMsg == "requestPayment:ok") {
									that.order_no = ''
									uni.redirectTo({
										url: "/packageA/my/orderList?status=" + 3
									})
									console.log('支付成功', res)
								} else {
									that.uloadingShow = false
									console.log('支付失败')
								}
							},
							fail(res) {
								that.uloadingShow = false
								console.log('支付失败', res)
							}
						})
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
		},
	}
</script>

<style lang="scss" scoped>
	.detail_all {
		background-color: #f7f7f7;
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}

	.con-center {
		// background: white;
		border-radius: 44rpx;
		position: relative;
		margin-top: 30rpx;

	}



	.space-between {
		justify-content: space-between;
	}

	.swiper {
		width: 100%;
		height: 580rpx;
	}

	.box {
		position: relative;
		margin: 0 30rpx;
	}


	.first-box {
		width: 690rpx;
		background: #FFFFFF;
		padding: 30rpx;
		border-radius: 44rpx;
		box-sizing: border-box;

		.head_img {
			width: 160rpx;
			height: 160rpx;
			border-radius: 18rpx;
		}

		.first-name {
			width: 440rpx;
			height: 39rpx auto;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #323232;
			// margin-left: 16rpx;
		}



		.first-image {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;

			text {
				// height: 80rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #222222;
			}

			.xieyi {
				background-color: #BBFC5B;
				width: 156rpx;
				height: 48rpx;
				border-radius: 8rpx;
			}
		}
	}

	.second-box {
		width: 690rpx;
		height: 64rpx;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		margin: 20rpx 0 16rpx 0;


		.number {
			height: 40rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 26rpx;
			color: #323232;
			margin-left: 32rpx;
		}

		view {
			text {
				width: 24rpx;
				height: 24rpx;
				background: rgba(255, 255, 255, 0.4);
				border-radius: 24rpx;
				margin: 0 22rpx 0 4rpx;
			}
		}
	}

	.third {
		width: 100%;
		background: #ffffff;
		margin-top: 20rpx;
		border-radius: 44rpx;

		.third-top {
			font-family: PingFang SC, PingFang SC;
			font-weight: 700;
			font-size: 36rpx;
			color: #323232;
			line-height: 50rpx;
			margin-top: 30rpx;
			margin-left: 30rpx;
			position: relative;
			z-index: 10;

			view {
				position: relative;

				.icons {
					width: 37rpx;
					height: 20rpx;
					position: absolute;
					left: 0;
					bottom: 0;
					z-index: -1;
				}
			}
		}

		.thirdCrode-center {
			/* 其他样式保持不变 */
			min-height: 400rpx;
			/* 确保有足够空间 */
			position: relative;

			.swiper-box {
				width: 100%;
				height: 350rpx;
				/* 与组件内高度一致 */
			}
			
			.bottom {
				width: 100%;
				margin-bottom: 20rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 26rpx;
				color: #9C9C9C;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
			
		}


		.third-center {
			padding: 30rpx;
			position: relative;

			.hui {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #9C9C9C;
			}

			.bottom {
				width: 100%;
				margin-bottom: 10rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 26rpx;
				color: #9C9C9C;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}



	}

	.line {
		width: 630rpx;
		height: 1rpx;
		background: #F0F0F0;
		margin: 30rpx 0;
	}

	.icon-size {
		width: 32rpx;
		height: 32rpx;
		margin-right: 12rpx;
	}


	.footer {
		width: 100%;
		height: 166rpx;
		margin-top: 50rpx;

		.footer-right {
			width: 690rpx;
			height: 90rpx;
			background: #323232;
			border-radius: 148rpx;
			margin-bottom: 30rpx;

			text {
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 36rpx;
				color: #BBFC5B;
			}
		}

		.footer-right1 {
			width: 690rpx;
			height: 90rpx;
			background: #E4E4E4;
			border-radius: 148rpx;

			text {
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 36rpx;
				color: #9C9C9C;
			}
		}
	}

	.popupBox {
		width: 640rpx;
		background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/confirm2.png");
		background-size: 100% 100%;
		background-repeat: no-repeat;
		border-radius: 44rpx;
		padding-bottom: 30rpx;

		.pop-header {
			width: 100%;

			background-repeat: no-repeat;
			background-position: left bottom;
			margin-top: 50rpx;

			span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 20rpx;
				color: #343434;
			}

			.name {
				width: 320rpx;
				height: 36rpx;
				font-family: PingFang SC Bold, PingFang SC Bold;
				font-weight: 600;
				font-size: 36rpx;
				color: #202020;
				line-height: 36rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}

			.price {
				width: 520rpx;
				height: 68rpx;
				margin-top: 30rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 28rpx;
				color: #202020;
				line-height: 34rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}

		.popup {
			display: flex;
			align-items: self-start;
			justify-content: center;
			width: 594rpx;
		}

		.popup-footer {
			position: absolute;
			left: 75rpx;
			bottom: 60rpx;

			span {
				width: 230rpx;
				height: 90rpx;
				background: #323232;
				border-radius: 200rpx 200rpx 200rpx 200rpx;
				font-family: PingFang SC Regular, PingFang SC Regular;
				font-weight: 400;
				font-size: 32rpx;
				color: #BBFC5B;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.span1 {
				background: rgba(193, 193, 193, 0.22);
				color: #202020;
				margin-right: 30rpx;
			}

		}


		.line {
			width: 642rpx;
			height: 1rpx;
			background: #F0F0F0;
			box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}



		.selectTime.selected {
			width: 288rpx;
			height: 50rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			background: #008CFF;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #FFFFFF;
			cursor: pointer;
			margin: 24rpx 32rpx 0 0;

		}
	}

	.share {
		position: fixed;
		color: #FFFFFF;
		right: 0;
		bottom: 190rpx;
		background: linear-gradient(to bottom right, #FE726B, #FE956B);
		padding: 10rpx 10rpx 10rpx 20rpx;
		border-top-left-radius: 50px;
		border-bottom-left-radius: 50px;
		box-shadow: 0 0 20upx rgba(0, 0, 0, .09);
	}

	.cancel {
		width: 100vw;
		padding: 30rpx;
		text-align: center;
		background: #FFFFFF;
		color: red;
		font-weight: bold;
		font-size: 30rpx;
	}

	.md-content {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;
		background-color: white;
	}

	.md-content-item {
		margin: 0 70rpx;
		position: relative;
	}

	.md-content-item image {
		width: 100rpx;
		height: 100rpx;
	}

	.md-content-item view {
		margin-top: 15rpx;
		font-size: 28rpx;
	}

	.sharebtn {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		opacity: 0;
	}

	.cu-modal {
		position: fixed;
		bottom: 166rpx;
		left: 0;
		z-index: 999999;
	}

	.gj {
		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 32rpx;
			color: #4B4B4B;

			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #4B4B4B;
			}
		}

		.scroll {
			width: 642rpx;
			max-height: 340rpx;

			view {
				margin: 24rpx;
				width: 600rpx;
				height: 56rpx;
				background: #E8E8E8;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #7A7A7A;
				}

				.red {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #FF5F5F;
				}

				.lan {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #008CFF;
				}
			}
		}
	}

	::v-deep ::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 4px !important;
		height: 1px !important;
		overflow: auto !important;
		background: #ccc !important;
		-webkit-appearance: auto !important;
		display: block;
	}

	::v-deep ::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 10px !important;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		background: #7b7979 !important;
	}

	::v-deep ::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		// border-radius: 10px !important;
		background: #FFFFFF !important;
	}

	.Poster {
		position: relative;
		top: 21rpx;
		left: 30rpx;
		width: 690rpx;
	}

	.posterClose {
		position: absolute;
		right: 8rpx;
		top: 8rpx;
	}

	.pos {
		position: relative;
	}

	.btnList {
		width: 690rpx;
		position: absolute;
		bottom: 150rpx;
		left: 30rpx;
		display: flex;
		justify-content: space-evenly;

		text {
			width: 250rpx;
			height: 80rpx;
			background: #FFFFFF;
			border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #008CFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.save {}
	}

	.no-scroll {
		overflow: hidden;
		height: 100vh;
	}

	::v-deep ._root {
		padding: 0 10rpx;
	}

	.no-border-button {
		background-color: transparent;
		/* 去掉背景色 */
		border: none;
		/* 去掉边框 */
		padding: 0;
		/* 去掉内边距 */
		margin: 0;
		/* 去掉外边距 */
		display: inline-flex;
		/* 使按钮内容居中 */
		align-items: center;
		/* 垂直居中 */
		justify-content: center;
		/* 水平居中 */
		flex-flow: column;
		height: 80rpx;
		line-height: inherit;

		text {
			width: 250rpx;
			height: 80rpx;
			background: #FFFFFF;
			border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #008CFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.btn_1 {
		width: 100%;
		height: 90rpx;
		background: #323232;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #BBFC5B;
		line-height: 90rpx;
		text-align: center;
	}

	.btn_2 {
		width: 100%;
		height: 90rpx;
		background: #E2E2E2;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #999999;
		line-height: 90rpx;
		text-align: center;
	}
</style>